import { before, describe, it, after } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import dayjs from 'dayjs'
import { EndToEndTestSetup } from '../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { TestAuthContext } from '../../../../../test/utils/test-auth-context.js'
import { TestUser } from '../../../../app/users/tests/setup-user.type.js'
import { HolidayRecurrence } from '../../holiday-recurrence.enum.js'
import { CreateHolidayCommandBuilder } from './create-holiday-command.builder.js'

describe('Create holiday end to end tests', () => {
  let setup: EndToEndTestSetup
  let context: TestAuthContext
  let adminUser: TestUser
  let defaultUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    context = setup.authContext

    adminUser = await context.getAdminUser()
    defaultUser = await context.getDefaultUser()
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const createHolidayDto = new CreateHolidayCommandBuilder()
      .withName('should-return-401-when-not-authenticated')
      .build()

    const response = await request(setup.httpServer)
      .post('/holidays')
      .send(createHolidayDto)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when not authorized', async () => {
    const createHolidayDto = new CreateHolidayCommandBuilder()
      .withName('should-return-403-when-not-authorized')
      .build()

    const response = await request(setup.httpServer)
      .post('/roles')
      .set('Authorization', `Bearer ${defaultUser.token}`)
      .send(createHolidayDto)

    expect(response).toHaveStatus(403)
  })

  it('creates holiday', async () => {
    const createHolidayDto = new CreateHolidayCommandBuilder()
      .withName('Kerstmis')
      .withStartDate(dayjs().add(1, 'year').format('YYYY-MM-DD'))
      .withIsRecurring(true)
      .withRecurrence(HolidayRecurrence.YEARLY)
      .build()

    const response = await request(setup.httpServer)
      .post('/holidays')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(createHolidayDto)

    expect(response).toHaveStatus(201)
  })
})
