import { before, describe, it } from 'node:test'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { InsertResult, Repository } from 'typeorm'
import dayjs from 'dayjs'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../modules/domain-events/domain-event-emitter.js'
import { Holiday } from '../../entities/holiday.entity.js'
import { generateHolidayUuid } from '../../entities/holiday.uuid.js'
import { InvalidHolidayStartDateError } from '../../errors/invalid-holiday-start-date.error.js'
import { CreateHolidayUseCase } from './create-holiday.use-case.js'
import { CreateHolidayCommandBuilder } from './create-holiday-command.builder.js'
import { HolidayCreatedEvent } from './holiday-created.event.js'

describe('CreateHolidayUseCase Unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('emits an event when creating a holiday', async () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)

    const holidayUuid = generateHolidayUuid()
    const repository = createStubInstance(Repository<Holiday>)
    repository.create.returns({})
    repository.insert.callsFake((holiday: Holiday) => {
      holiday.uuid = holidayUuid
      return Promise.resolve({} as InsertResult)
    })

    const useCase = new CreateHolidayUseCase(stubDataSource(), eventEmitter, repository)
    const command = new CreateHolidayCommandBuilder()
      .withName('Test Holiday')
      .withStartDate(dayjs().add(1, 'day').format('YYYY-MM-DD'))
      .withIsRecurring(false)
      .build()

    await useCase.execute(command)

    expect(eventEmitter).toHaveEmitted(new HolidayCreatedEvent(holidayUuid))
  })

  it('returns a validation error when end date is before start date', () => {
    const command = new CreateHolidayCommandBuilder()
      .withStartDate('2023-12-26')
      .withEndDate('2023-12-25')
      .build()

    expect(command).toHaveValidationErrors()
  })

  it('returns invalid_holiday_start_date when start date is in the past', () => {
    const holidayUuid = generateHolidayUuid()
    const repository = createStubInstance(Repository<Holiday>)
    repository.insert.callsFake(() => {
      return Promise.resolve({ identifiers: [{ uuid: holidayUuid }] } as unknown as InsertResult)
    })

    const eventEmitter = createStubInstance(DomainEventEmitter)

    const useCase = new CreateHolidayUseCase(stubDataSource(), eventEmitter, repository)
    const command = new CreateHolidayCommandBuilder()
      .withName('Test Holiday')
      .withStartDate(dayjs().subtract(1, 'month').format('YYYY-MM-DD'))
      .withIsRecurring(false)
      .build()

    expect(useCase.execute(command)).rejects.toThrow(InvalidHolidayStartDateError)
  })
})
